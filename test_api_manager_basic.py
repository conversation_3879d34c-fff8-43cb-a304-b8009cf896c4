#!/usr/bin/env python3
"""
Basic test to verify API manager functionality
Quick smoke test for the API key management system
"""

import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from api_manager import APIManager, APIProvider, SecureKeyStorage
        print("  ✅ APIManager imported successfully")
    except Exception as e:
        print(f"  ❌ Failed to import APIManager: {e}")
        return False
    
    try:
        from api_key_cli import APIKeyCLI
        print("  ✅ APIKeyCLI imported successfully")
    except Exception as e:
        print(f"  ❌ Failed to import APIKeyCLI: {e}")
        return False
    
    return True

def test_api_providers():
    """Test API provider enumeration"""
    print("\n🔧 Testing API providers...")
    
    try:
        from api_manager import APIProvider
        
        providers = list(APIProvider)
        print(f"  ✅ Found {len(providers)} providers: {[p.value for p in providers]}")
        
        # Check for required providers
        required_providers = ["openai", "anthropic", "azure", "local"]
        for required in required_providers:
            if not any(p.value == required for p in providers):
                print(f"  ❌ Missing required provider: {required}")
                return False
        
        print("  ✅ All required providers found")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing providers: {e}")
        return False

def test_api_manager_creation():
    """Test API manager creation"""
    print("\n🏗️ Testing API manager creation...")
    
    try:
        from api_manager import APIManager
        
        manager = APIManager()
        print("  ✅ APIManager created successfully")
        
        # Test basic methods
        providers = manager.get_available_providers()
        print(f"  ✅ Available providers: {len(providers)}")
        
        configured = manager.list_configured_providers()
        print(f"  ✅ Configured providers: {len(configured)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error creating API manager: {e}")
        return False

def test_secure_storage():
    """Test secure storage functionality"""
    print("\n🔐 Testing secure storage...")
    
    try:
        from api_manager import SecureKeyStorage
        import tempfile
        
        # Create temporary storage
        temp_dir = tempfile.mkdtemp()
        storage_path = Path(temp_dir) / "test_keys.enc"
        storage = SecureKeyStorage(storage_path)
        
        print("  ✅ SecureKeyStorage created successfully")
        
        # Test basic operations
        test_key = "sk-test123456789"
        storage.store_key("test_provider", test_key)
        print("  ✅ Key stored successfully")
        
        loaded_key = storage.load_key("test_provider")
        if loaded_key == test_key:
            print("  ✅ Key loaded successfully")
        else:
            print(f"  ❌ Key mismatch: expected {test_key}, got {loaded_key}")
            return False
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing secure storage: {e}")
        return False

def test_cli_creation():
    """Test CLI creation"""
    print("\n💻 Testing CLI creation...")
    
    try:
        from api_key_cli import APIKeyCLI
        
        cli = APIKeyCLI()
        print("  ✅ APIKeyCLI created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error creating CLI: {e}")
        return False

def main():
    """Run basic tests"""
    print("🧪 Aetherforge API Key Management - Basic Tests")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("API Providers Test", test_api_providers),
        ("API Manager Test", test_api_manager_creation),
        ("Secure Storage Test", test_secure_storage),
        ("CLI Test", test_cli_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("📊 BASIC TEST RESULTS")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All basic tests passed! API key management system is functional.")
        return True
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
