"""
Enhanced API Manager for Aetherforge
Provides secure API key management, robust API integration with multiple providers,
fallback mechanisms, retry logic, rate limiting, and comprehensive configuration management.
"""

import asyncio
import time
import logging
import os
import hashlib
import base64
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path
import aiohttp
import openai
from openai import AsyncOpenAI
import anthropic
from anthropic import AsyncAnthropic
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import traceback
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/api_manager.log', mode='a')
    ]
)

logger = logging.getLogger(__name__)

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

class APIProvider(Enum):
    """Supported API providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    AZURE = "azure"
    LOCAL = "local"
    OLLAMA = "ollama"

class SecureKeyStorage:
    """Secure storage for API keys using encryption"""

    def __init__(self, storage_path: Optional[str] = None):
        self.storage_path = Path(storage_path or os.path.expanduser("~/.aetherforge/keys.enc"))
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        self._key = self._get_or_create_key()
        self._fernet = Fernet(self._key)

    def _get_or_create_key(self) -> bytes:
        """Get or create encryption key"""
        key_file = self.storage_path.parent / "key.key"

        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            password = os.getenv("AETHERFORGE_MASTER_KEY", "aetherforge-default-key").encode()
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))

            # Save key and salt
            with open(key_file, 'wb') as f:
                f.write(key)
            with open(self.storage_path.parent / "salt", 'wb') as f:
                f.write(salt)

            return key

    def store_key(self, provider: str, api_key: str) -> None:
        """Store an encrypted API key"""
        try:
            logger.debug(f"Attempting to store API key for provider: {provider}")

            # Validate inputs
            if not provider or not isinstance(provider, str):
                raise ValueError("Provider must be a non-empty string")
            if not api_key or not isinstance(api_key, str):
                raise ValueError("API key must be a non-empty string")

            # Load existing keys
            keys = self.load_all_keys()

            # Add/update key
            keys[provider] = api_key

            # Ensure directory exists
            self.storage_path.parent.mkdir(parents=True, exist_ok=True)

            # Encrypt and save
            encrypted_data = self._fernet.encrypt(json.dumps(keys).encode())

            # Write to temporary file first, then rename for atomic operation
            temp_path = self.storage_path.with_suffix('.tmp')
            with open(temp_path, 'wb') as f:
                f.write(encrypted_data)

            # Atomic rename
            temp_path.replace(self.storage_path)

            logger.info(f"Successfully stored API key for provider: {provider}")

        except ValueError as e:
            logger.error(f"Invalid input for storing API key: {e}")
            raise
        except PermissionError as e:
            logger.error(f"Permission denied when storing API key for {provider}: {e}")
            raise RuntimeError(f"Cannot write to key storage: {e}")
        except Exception as e:
            logger.error(f"Unexpected error storing API key for {provider}: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            raise RuntimeError(f"Failed to store API key: {e}")

    def load_key(self, provider: str) -> Optional[str]:
        """Load a decrypted API key"""
        try:
            logger.debug(f"Attempting to load API key for provider: {provider}")

            if not provider or not isinstance(provider, str):
                logger.warning(f"Invalid provider name: {provider}")
                return None

            keys = self.load_all_keys()
            key = keys.get(provider)

            if key:
                logger.debug(f"Successfully loaded API key for provider: {provider}")
            else:
                logger.debug(f"No API key found for provider: {provider}")

            return key

        except Exception as e:
            logger.warning(f"Failed to load API key for {provider}: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return None

    def load_all_keys(self) -> Dict[str, str]:
        """Load all decrypted API keys"""
        try:
            if not self.storage_path.exists():
                logger.debug("Key storage file does not exist, returning empty dict")
                return {}

            logger.debug(f"Loading keys from: {self.storage_path}")

            with open(self.storage_path, 'rb') as f:
                encrypted_data = f.read()

            if not encrypted_data:
                logger.warning("Key storage file is empty")
                return {}

            decrypted_data = self._fernet.decrypt(encrypted_data)
            keys = json.loads(decrypted_data.decode())

            logger.debug(f"Successfully loaded {len(keys)} API keys")
            return keys

        except FileNotFoundError:
            logger.debug("Key storage file not found, returning empty dict")
            return {}
        except PermissionError as e:
            logger.error(f"Permission denied reading key storage: {e}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in key storage: {e}")
            return {}
        except Exception as e:
            logger.warning(f"Failed to load API keys: {e}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return {}

    def delete_key(self, provider: str) -> bool:
        """Delete an API key"""
        try:
            keys = self.load_all_keys()
            if provider in keys:
                del keys[provider]

                # Save updated keys
                encrypted_data = self._fernet.encrypt(json.dumps(keys).encode())
                with open(self.storage_path, 'wb') as f:
                    f.write(encrypted_data)

                logger.info(f"Deleted API key for provider: {provider}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete API key for {provider}: {e}")
            return False

    def list_providers(self) -> List[str]:
        """List all providers with stored keys"""
        keys = self.load_all_keys()
        return list(keys.keys())

class APIKeyValidator:
    """Validates API keys for different providers"""

    @staticmethod
    async def validate_openai_key(api_key: str) -> Dict[str, Any]:
        """Validate OpenAI API key"""
        try:
            client = AsyncOpenAI(api_key=api_key)

            # Test with a simple completion
            response = await client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )

            return {
                "valid": True,
                "provider": "openai",
                "model_access": ["gpt-3.5-turbo"],
                "message": "API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg:
                return {"valid": False, "provider": "openai", "message": "Invalid API key"}
            elif "quota" in error_msg:
                return {"valid": False, "provider": "openai", "message": "API quota exceeded"}
            else:
                return {"valid": False, "provider": "openai", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_anthropic_key(api_key: str) -> Dict[str, Any]:
        """Validate Anthropic API key"""
        try:
            client = AsyncAnthropic(api_key=api_key)

            # Test with a simple message
            response = await client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=5,
                messages=[{"role": "user", "content": "Hello"}]
            )

            return {
                "valid": True,
                "provider": "anthropic",
                "model_access": ["claude-3-haiku-20240307"],
                "message": "API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg:
                return {"valid": False, "provider": "anthropic", "message": "Invalid API key"}
            elif "quota" in error_msg or "credit" in error_msg:
                return {"valid": False, "provider": "anthropic", "message": "API quota exceeded"}
            else:
                return {"valid": False, "provider": "anthropic", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_azure_key(api_key: str, base_url: str, api_version: str = "2024-02-01") -> Dict[str, Any]:
        """Validate Azure OpenAI API key"""
        try:
            from openai import AsyncAzureOpenAI

            client = AsyncAzureOpenAI(
                api_key=api_key,
                azure_endpoint=base_url,
                api_version=api_version
            )

            # Test with a simple completion
            response = await client.chat.completions.create(
                model="gpt-35-turbo",  # Common Azure deployment name
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )

            return {
                "valid": True,
                "provider": "azure",
                "model_access": ["gpt-35-turbo", "gpt-4"],
                "message": "Azure API key is valid"
            }
        except Exception as e:
            error_msg = str(e).lower()
            if "invalid" in error_msg or "unauthorized" in error_msg:
                return {"valid": False, "provider": "azure", "message": "Invalid API key or endpoint"}
            elif "quota" in error_msg:
                return {"valid": False, "provider": "azure", "message": "API quota exceeded"}
            elif "deployment" in error_msg:
                return {"valid": False, "provider": "azure", "message": "Deployment not found - check model deployment name"}
            else:
                return {"valid": False, "provider": "azure", "message": f"Validation failed: {e}"}

    @staticmethod
    async def validate_local_endpoint(base_url: str) -> Dict[str, Any]:
        """Validate local API endpoint"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{base_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [model.get("name", "unknown") for model in data.get("models", [])]
                        return {
                            "valid": True,
                            "provider": "local",
                            "model_access": models,
                            "message": "Local endpoint is accessible"
                        }
                    else:
                        return {"valid": False, "provider": "local", "message": f"Endpoint returned status {response.status}"}
        except Exception as e:
            return {"valid": False, "provider": "local", "message": f"Endpoint validation failed: {e}"}

    @classmethod
    async def validate_key(cls, provider: APIProvider, api_key: str, base_url: Optional[str] = None, api_version: Optional[str] = None) -> Dict[str, Any]:
        """Validate API key for any provider"""
        if provider == APIProvider.OPENAI:
            return await cls.validate_openai_key(api_key)
        elif provider == APIProvider.ANTHROPIC:
            return await cls.validate_anthropic_key(api_key)
        elif provider == APIProvider.AZURE:
            if not base_url:
                return {"valid": False, "provider": "azure", "message": "Azure endpoint URL is required"}
            return await cls.validate_azure_key(api_key, base_url, api_version or "2024-02-01")
        elif provider in [APIProvider.LOCAL, APIProvider.OLLAMA]:
            return await cls.validate_local_endpoint(base_url or "http://localhost:11434")
        else:
            return {"valid": False, "provider": provider.value, "message": "Unsupported provider"}

@dataclass
class APIConfig:
    """Configuration for API providers"""
    provider: APIProvider
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-4"
    max_tokens: int = 2000
    temperature: float = 0.7
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_rpm: int = 60  # requests per minute
    # Azure-specific parameters
    api_version: Optional[str] = None
    deployment_name: Optional[str] = None

@dataclass
class RateLimiter:
    """Rate limiter for API calls"""
    max_requests: int
    time_window: int = 60  # seconds
    requests: List[float] = field(default_factory=list)
    
    def can_make_request(self) -> bool:
        """Check if we can make a request within rate limits"""
        now = time.time()
        # Remove old requests outside the time window
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """Record a new request"""
        self.requests.append(time.time())
    
    def time_until_next_request(self) -> float:
        """Get time to wait until next request is allowed"""
        if self.can_make_request():
            return 0.0
        
        now = time.time()
        oldest_request = min(self.requests)
        return self.time_window - (now - oldest_request)

class APIError(Exception):
    """Base API error"""
    def __init__(self, message: str, provider: APIProvider, retryable: bool = True):
        super().__init__(message)
        self.provider = provider
        self.retryable = retryable

class QuotaExceededError(APIError):
    """API quota exceeded error"""
    def __init__(self, provider: APIProvider):
        super().__init__(f"API quota exceeded for {provider.value}", provider, False)

class RateLimitError(APIError):
    """Rate limit exceeded error"""
    def __init__(self, provider: APIProvider, retry_after: float = 60):
        super().__init__(f"Rate limit exceeded for {provider.value}", provider, True)
        self.retry_after = retry_after

class APIManager:
    """Enhanced API manager with secure storage, multi-provider support and robust error handling"""

    def __init__(self, config_file: Optional[str] = None, storage_path: Optional[str] = None):
        self.providers: Dict[APIProvider, APIConfig] = {}
        self.clients: Dict[APIProvider, Any] = {}
        self.rate_limiters: Dict[APIProvider, RateLimiter] = {}
        self.fallback_order: List[APIProvider] = []
        self.secure_storage = SecureKeyStorage(storage_path)
        self.validator = APIKeyValidator()

        # Load configuration
        self._load_configuration(config_file)
        self._initialize_clients()
        self._setup_rate_limiters()

    async def set_api_key(self, provider: APIProvider, api_key: str, validate: bool = True) -> Dict[str, Any]:
        """Set and optionally validate an API key"""
        result = {"success": False, "message": ""}

        try:
            # Validate key if requested
            if validate:
                validation_result = await self.validator.validate_key(provider, api_key)
                if not validation_result["valid"]:
                    result["message"] = validation_result["message"]
                    return result
                result["validation"] = validation_result

            # Store key securely
            self.secure_storage.store_key(provider.value, api_key)

            # Update configuration
            if provider in self.providers:
                self.providers[provider].api_key = api_key
            else:
                # Create new config
                self.providers[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    model=self._get_default_model(provider)
                )

            # Reinitialize client
            await self._initialize_client(provider)

            result["success"] = True
            result["message"] = f"API key set successfully for {provider.value}"
            logger.info(f"API key configured for {provider.value}")

        except Exception as e:
            result["message"] = f"Failed to set API key: {e}"
            logger.error(f"Failed to set API key for {provider.value}: {e}")

        return result

    def get_api_key(self, provider: APIProvider) -> Optional[str]:
        """Get API key for a provider"""
        # First check secure storage
        stored_key = self.secure_storage.load_key(provider.value)
        if stored_key:
            return stored_key

        # Fallback to environment variables
        env_var_map = {
            APIProvider.OPENAI: "OPENAI_API_KEY",
            APIProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
            APIProvider.AZURE: "AZURE_OPENAI_API_KEY",
            APIProvider.LOCAL: "LOCAL_API_KEY",
            APIProvider.OLLAMA: "OLLAMA_API_KEY"
        }

        env_var = env_var_map.get(provider)
        if env_var:
            return os.getenv(env_var)

        return None

    def remove_api_key(self, provider: APIProvider) -> bool:
        """Remove an API key"""
        try:
            # Remove from secure storage
            removed = self.secure_storage.delete_key(provider.value)

            # Remove from active configuration
            if provider in self.providers:
                del self.providers[provider]

            if provider in self.clients:
                del self.clients[provider]

            if provider in self.rate_limiters:
                del self.rate_limiters[provider]

            logger.info(f"Removed API key for {provider.value}")
            return removed
        except Exception as e:
            logger.error(f"Failed to remove API key for {provider.value}: {e}")
            return False

    def list_configured_providers(self) -> List[Dict[str, Any]]:
        """List all configured providers with their status"""
        providers = []

        for provider in APIProvider:
            api_key = self.get_api_key(provider)
            has_key = bool(api_key)
            is_active = provider in self.providers

            providers.append({
                "provider": provider.value,
                "has_key": has_key,
                "is_active": is_active,
                "model": self.providers[provider].model if is_active else self._get_default_model(provider)
            })

        return providers

    def _get_default_model(self, provider: APIProvider) -> str:
        """Get default model for a provider"""
        defaults = {
            APIProvider.OPENAI: "gpt-4",
            APIProvider.ANTHROPIC: "claude-3-sonnet-20240229",
            APIProvider.AZURE: "gpt-35-turbo",
            APIProvider.LOCAL: "llama2",
            APIProvider.OLLAMA: "llama2"
        }
        return defaults.get(provider, "unknown")
    
    def _load_configuration(self, config_file: Optional[str] = None) -> None:
        """
        Load API configuration from secure storage, file, or environment variables.

        Args:
            config_file: Optional path to JSON configuration file

        Note:
            Configuration is loaded in the following priority:
            1. Secure storage (encrypted keys)
            2. Configuration file (if provided and exists)
            3. Environment variables
            4. Default values
        """

        # Load stored keys
        stored_keys = self.secure_storage.load_all_keys()

        # Default configurations with secure storage integration
        default_configs = {}

        for provider in APIProvider:
            # Get API key from secure storage first, then environment
            api_key = stored_keys.get(provider.value) or self.get_api_key(provider) or ""

            if provider == APIProvider.OPENAI:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    model=os.getenv("OPENAI_MODEL", "gpt-4"),
                    max_tokens=int(os.getenv("OPENAI_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("OPENAI_RATE_LIMIT", "60"))
                )
            elif provider == APIProvider.ANTHROPIC:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    model=os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
                    max_tokens=int(os.getenv("ANTHROPIC_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("ANTHROPIC_RATE_LIMIT", "60"))
                )
            elif provider == APIProvider.AZURE:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key,
                    base_url=os.getenv("AZURE_OPENAI_ENDPOINT", ""),
                    model=os.getenv("AZURE_OPENAI_MODEL", "gpt-35-turbo"),
                    max_tokens=int(os.getenv("AZURE_MAX_TOKENS", "2000")),
                    rate_limit_rpm=int(os.getenv("AZURE_RATE_LIMIT", "60")),
                    api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01"),
                    deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-35-turbo")
                )
            elif provider == APIProvider.LOCAL:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key or "local",
                    base_url=os.getenv("LOCAL_API_URL", "http://localhost:11434"),
                    model=os.getenv("LOCAL_MODEL", "llama2"),
                    rate_limit_rpm=int(os.getenv("LOCAL_RATE_LIMIT", "120"))
                )
            elif provider == APIProvider.OLLAMA:
                default_configs[provider] = APIConfig(
                    provider=provider,
                    api_key=api_key or "ollama",
                    base_url=os.getenv("OLLAMA_API_URL", "http://localhost:11434"),
                    model=os.getenv("OLLAMA_MODEL", "llama2"),
                    rate_limit_rpm=int(os.getenv("OLLAMA_RATE_LIMIT", "120"))
                )
        
        # Load from config file if provided
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    # Update default configs with file configs
                    for provider_name, config_data in file_config.items():
                        provider = APIProvider(provider_name)
                        if provider in default_configs:
                            # Update existing config
                            for key, value in config_data.items():
                                if hasattr(default_configs[provider], key):
                                    setattr(default_configs[provider], key, value)
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
        
        # Only add providers with valid API keys
        for provider, config in default_configs.items():
            if config.api_key and config.api_key != "":
                self.providers[provider] = config
                logger.info(f"Configured API provider: {provider.value}")
        
        # Set fallback order based on available providers
        self.fallback_order = [
            APIProvider.OPENAI,
            APIProvider.AZURE,
            APIProvider.ANTHROPIC,
            APIProvider.LOCAL
        ]
        self.fallback_order = [p for p in self.fallback_order if p in self.providers]
        
        if not self.providers:
            logger.warning("No API providers configured with valid keys")

    def _initialize_clients(self):
        """Initialize API clients for each provider"""
        for provider in list(self.providers.keys()):
            try:
                # Try to get running event loop
                loop = asyncio.get_running_loop()
                loop.create_task(self._initialize_client(provider))
            except RuntimeError:
                # No running event loop, skip async initialization
                # Clients will be initialized on first use
                pass

    async def _initialize_client(self, provider: APIProvider):
        """Initialize API client for a specific provider"""
        if provider not in self.providers:
            return

        config = self.providers[provider]
        try:
            if provider == APIProvider.OPENAI:
                self.clients[provider] = AsyncOpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url,
                    timeout=config.timeout
                )
            elif provider == APIProvider.ANTHROPIC:
                self.clients[provider] = AsyncAnthropic(
                    api_key=config.api_key,
                    timeout=config.timeout
                )
            elif provider == APIProvider.AZURE:
                from openai import AsyncAzureOpenAI
                self.clients[provider] = AsyncAzureOpenAI(
                    api_key=config.api_key,
                    azure_endpoint=config.base_url,
                    api_version=config.api_version or "2024-02-01",
                    timeout=config.timeout
                )
            elif provider in [APIProvider.LOCAL, APIProvider.OLLAMA]:
                # For local/Ollama, we'll use aiohttp
                self.clients[provider] = aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=config.timeout)
                )

            logger.info(f"Initialized client for {provider.value}")
        except Exception as e:
            logger.error(f"Failed to initialize client for {provider.value}: {e}")
            # Remove provider if client initialization fails
            if provider in self.providers:
                del self.providers[provider]
    
    def _setup_rate_limiters(self):
        """Setup rate limiters for each provider"""
        for provider, config in self.providers.items():
            self.rate_limiters[provider] = RateLimiter(
                max_requests=config.rate_limit_rpm,
                time_window=60
            )
    
    async def _wait_for_rate_limit(self, provider: APIProvider):
        """Wait if rate limit is exceeded"""
        rate_limiter = self.rate_limiters.get(provider)
        if rate_limiter and not rate_limiter.can_make_request():
            wait_time = rate_limiter.time_until_next_request()
            logger.info(f"Rate limit reached for {provider.value}, waiting {wait_time:.1f}s")
            await asyncio.sleep(wait_time)
    
    async def _make_request_with_retry(
        self, 
        provider: APIProvider, 
        request_func: Callable,
        *args, 
        **kwargs
    ) -> Any:
        """Make API request with exponential backoff retry"""
        config = self.providers[provider]
        
        for attempt in range(config.max_retries + 1):
            try:
                # Check rate limit
                await self._wait_for_rate_limit(provider)
                
                # Record request
                self.rate_limiters[provider].record_request()
                
                # Make request
                result = await request_func(*args, **kwargs)
                return result
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # Check for quota exceeded
                if "quota" in error_msg or "insufficient_quota" in error_msg:
                    raise QuotaExceededError(provider)
                
                # Check for rate limit
                if "rate" in error_msg and "limit" in error_msg:
                    retry_after = 60  # Default retry after 60 seconds
                    raise RateLimitError(provider, retry_after)
                
                # If this is the last attempt, raise the error
                if attempt == config.max_retries:
                    raise APIError(f"API request failed after {config.max_retries} retries: {e}", provider)
                
                # Exponential backoff
                wait_time = config.retry_delay * (2 ** attempt)
                logger.warning(f"API request failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
        
        raise APIError(f"API request failed after all retries", provider)

    async def generate_text(
        self, 
        messages: List[Dict[str, str]], 
        preferred_provider: Optional[APIProvider] = None,
        **kwargs
    ) -> str:
        """Generate text using the best available provider"""
        
        # Determine provider order
        providers_to_try = []
        if preferred_provider and preferred_provider in self.providers:
            providers_to_try.append(preferred_provider)
        
        # Add fallback providers
        for provider in self.fallback_order:
            if provider not in providers_to_try:
                providers_to_try.append(provider)
        
        if not providers_to_try:
            raise APIError("No API providers available", APIProvider.OPENAI, False)
        
        last_error = None
        
        for provider in providers_to_try:
            try:
                logger.debug(f"Attempting text generation with {provider.value}")
                result = await self._generate_with_provider(provider, messages, **kwargs)
                logger.info(f"Successfully generated text with {provider.value}")
                return result
                
            except QuotaExceededError as e:
                logger.warning(f"Quota exceeded for {provider.value}, trying next provider")
                last_error = e
                continue
                
            except RateLimitError as e:
                logger.warning(f"Rate limit exceeded for {provider.value}, trying next provider")
                last_error = e
                continue
                
            except APIError as e:
                if not e.retryable:
                    logger.error(f"Non-retryable error with {provider.value}: {e}")
                    last_error = e
                    continue
                else:
                    logger.warning(f"Retryable error with {provider.value}: {e}")
                    last_error = e
                    continue
        
        # If we get here, all providers failed
        if last_error:
            raise last_error
        else:
            raise APIError("All API providers failed", APIProvider.OPENAI, False)

    async def _generate_with_provider(
        self, 
        provider: APIProvider, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate text with a specific provider"""
        config = self.providers[provider]
        client = self.clients[provider]
        
        # Merge kwargs with config defaults
        generation_params = {
            "max_tokens": kwargs.get("max_tokens", config.max_tokens),
            "temperature": kwargs.get("temperature", config.temperature),
        }
        
        if provider == APIProvider.OPENAI:
            return await self._generate_openai(client, config, messages, generation_params)
        elif provider == APIProvider.ANTHROPIC:
            return await self._generate_anthropic(client, config, messages, generation_params)
        elif provider == APIProvider.AZURE:
            return await self._generate_azure(client, config, messages, generation_params)
        elif provider == APIProvider.LOCAL:
            return await self._generate_local(client, config, messages, generation_params)
        else:
            raise APIError(f"Unsupported provider: {provider.value}", provider, False)

    async def _generate_openai(self, client: AsyncOpenAI, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using OpenAI API"""
        async def request_func():
            response = await client.chat.completions.create(
                model=config.model,
                messages=messages,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"]
            )
            return response.choices[0].message.content
        
        return await self._make_request_with_retry(APIProvider.OPENAI, request_func)

    async def _generate_anthropic(self, client: AsyncAnthropic, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using Anthropic API"""
        async def request_func():
            # Convert messages format for Anthropic
            system_message = ""
            user_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    user_messages.append(msg)
            
            response = await client.messages.create(
                model=config.model,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"],
                system=system_message,
                messages=user_messages
            )
            return response.content[0].text
        
        return await self._make_request_with_retry(APIProvider.ANTHROPIC, request_func)

    async def _generate_azure(self, client, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using Azure OpenAI API"""
        async def request_func():
            response = await client.chat.completions.create(
                model=config.deployment_name or config.model,
                messages=messages,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"]
            )
            return response.choices[0].message.content

        return await self._make_request_with_retry(APIProvider.AZURE, request_func)

    async def _generate_local(self, client: aiohttp.ClientSession, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using local API (Ollama)"""
        async def request_func():
            # Convert to Ollama format
            prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
            
            payload = {
                "model": config.model,
                "prompt": prompt,
                "options": {
                    "num_predict": params["max_tokens"],
                    "temperature": params["temperature"]
                }
            }
            
            async with client.post(f"{config.base_url}/api/generate", json=payload) as response:
                if response.status != 200:
                    raise Exception(f"Local API error: {response.status}")
                
                result = await response.json()
                return result.get("response", "")
        
        return await self._make_request_with_retry(APIProvider.LOCAL, request_func)

    async def close(self):
        """Close all API clients"""
        for provider, client in self.clients.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                logger.warning(f"Error closing client for {provider.value}: {e}")

    def get_available_providers(self) -> List[APIProvider]:
        """Get list of available providers"""
        return list(self.providers.keys())

    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers"""
        status = {}
        for provider, config in self.providers.items():
            rate_limiter = self.rate_limiters[provider]
            status[provider.value] = {
                "configured": True,
                "model": config.model,
                "rate_limit_remaining": config.rate_limit_rpm - len(rate_limiter.requests),
                "can_make_request": rate_limiter.can_make_request()
            }
        return status

# Global API manager instance
_api_manager: Optional[APIManager] = None

def get_api_manager() -> APIManager:
    """Get global API manager instance"""
    global _api_manager
    if _api_manager is None:
        _api_manager = APIManager()
    return _api_manager

async def generate_text(messages: List[Dict[str, str]], **kwargs) -> str:
    """Convenience function for text generation"""
    api_manager = get_api_manager()
    return await api_manager.generate_text(messages, **kwargs)
