#!/usr/bin/env python3
"""
Aetherforge Main Entry Point
Provides unified command-line interface for all Aetherforge functionality including API key management.
"""

import sys
import os
import argparse
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Main entry point for Aetherforge CLI"""
    parser = argparse.ArgumentParser(
        description="Aetherforge - Autonomous AI Software Creation System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Commands:
  keys        Manage API keys for AI providers
  create      Create a new project
  start       Start Aetherforge services
  status      Check system status
  test        Run system tests

Examples:
  python main.py keys setup                    # Setup API keys
  python main.py keys list                     # List configured providers
  python main.py create "todo app"             # Create a project
  python main.py start                         # Start all services
  python main.py status                        # Check system status
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # API Keys subcommand
    keys_parser = subparsers.add_parser('keys', help='Manage API keys')
    keys_parser.add_argument('action', choices=['setup', 'set', 'test', 'list', 'remove'], 
                           help='API key action')
    keys_parser.add_argument('provider', nargs='?', 
                           help='Provider name (openai, anthropic, azure, local)')
    keys_parser.add_argument('--key', help='API key (will prompt if not provided)')
    keys_parser.add_argument('--no-validate', action='store_true', help='Skip key validation')
    
    # Project creation subcommand
    create_parser = subparsers.add_parser('create', help='Create a new project')
    create_parser.add_argument('prompt', help='Project description')
    create_parser.add_argument('--name', help='Project name')
    create_parser.add_argument('--type', default='fullstack', 
                             choices=['fullstack', 'frontend', 'backend', 'mobile', 'desktop'],
                             help='Project type')
    create_parser.add_argument('--output', default='./projects', help='Output directory')
    
    # Start services subcommand
    start_parser = subparsers.add_parser('start', help='Start Aetherforge services')
    start_parser.add_argument('--orchestrator-only', action='store_true', 
                            help='Start only the orchestrator')
    start_parser.add_argument('--port', type=int, default=8000, help='Orchestrator port')
    
    # Status subcommand
    status_parser = subparsers.add_parser('status', help='Check system status')
    status_parser.add_argument('--detailed', action='store_true', help='Show detailed status')
    
    # Test subcommand
    test_parser = subparsers.add_parser('test', help='Run system tests')
    test_parser.add_argument('--component', help='Test specific component')
    test_parser.add_argument('--integration', action='store_true', help='Run integration tests')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'keys':
            handle_keys_command(args)
        elif args.command == 'create':
            asyncio.run(handle_create_command(args))
        elif args.command == 'start':
            asyncio.run(handle_start_command(args))
        elif args.command == 'status':
            asyncio.run(handle_status_command(args))
        elif args.command == 'test':
            asyncio.run(handle_test_command(args))
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def handle_keys_command(args):
    """Handle API key management commands"""
    from api_key_cli import APIKeyCLI
    
    cli = APIKeyCLI()
    
    if args.action == 'setup':
        asyncio.run(cli.setup_wizard())
    elif args.action == 'set':
        if not args.provider:
            print("❌ Provider name is required for 'set' action")
            sys.exit(1)
        validate = not args.no_validate
        asyncio.run(cli.set_key(args.provider, args.key, validate))
    elif args.action == 'test':
        if not args.provider:
            print("❌ Provider name is required for 'test' action")
            sys.exit(1)
        asyncio.run(cli.test_key(args.provider))
    elif args.action == 'list':
        cli.list_keys()
    elif args.action == 'remove':
        if not args.provider:
            print("❌ Provider name is required for 'remove' action")
            sys.exit(1)
        cli.remove_key(args.provider)

async def handle_create_command(args):
    """Handle project creation commands"""
    try:
        from project_generator_standalone import ProjectGenerator
        from api_manager import get_api_manager
        
        # Initialize API manager
        api_manager = get_api_manager()
        available_providers = api_manager.get_available_providers()
        
        if not available_providers:
            print("❌ No API providers configured. Run 'python main.py keys setup' first.")
            return
        
        print(f"🔮 Creating project with prompt: {args.prompt}")
        print(f"📁 Output directory: {args.output}")
        print(f"🏗️ Project type: {args.type}")
        
        generator = ProjectGenerator()
        
        result = await generator.generate_project(
            prompt=args.prompt,
            project_name=args.name or "generated_project",
            project_type=args.type,
            output_dir=args.output
        )
        
        if result.get('success'):
            print(f"✅ Project created successfully!")
            print(f"📁 Location: {result.get('project_path')}")
        else:
            print(f"❌ Project creation failed: {result.get('error')}")
            
    except ImportError as e:
        print(f"❌ Required components not available: {e}")
        print("💡 Make sure all dependencies are installed")

async def handle_start_command(args):
    """Handle service startup commands"""
    try:
        print("🚀 Starting Aetherforge services...")
        
        if args.orchestrator_only:
            print(f"🎯 Starting orchestrator on port {args.port}")
            # Import and start orchestrator
            from orchestrator import start_orchestrator
            await start_orchestrator(port=args.port)
        else:
            print("🌟 Starting all services")
            # Import and start all services
            import start_services
            await start_services.main()
            
    except ImportError as e:
        print(f"❌ Service components not available: {e}")
    except Exception as e:
        print(f"❌ Failed to start services: {e}")

async def handle_status_command(args):
    """Handle status check commands"""
    try:
        from api_manager import get_api_manager
        import aiohttp
        
        print("🔍 Checking Aetherforge system status...")
        
        # Check API providers
        print("\n🔑 API Providers:")
        api_manager = get_api_manager()
        providers = api_manager.list_configured_providers()
        
        for provider in providers:
            status = "✅ Active" if provider["is_active"] else ("🔑 Configured" if provider["has_key"] else "❌ Not configured")
            print(f"  {provider['provider'].upper():12} | {status}")
        
        # Check orchestrator
        print("\n🎯 Services:")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8000/health', timeout=5) as response:
                    if response.status == 200:
                        print("  Orchestrator     | ✅ Running")
                    else:
                        print("  Orchestrator     | ⚠️ Issues detected")
        except:
            print("  Orchestrator     | ❌ Not running")
        
        if args.detailed:
            print("\n📊 Detailed Status:")
            status = api_manager.get_provider_status()
            for provider, info in status.items():
                print(f"  {provider}:")
                print(f"    Model: {info['model']}")
                print(f"    Rate limit remaining: {info['rate_limit_remaining']}")
                print(f"    Can make request: {info['can_make_request']}")
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")

async def handle_test_command(args):
    """Handle test commands"""
    try:
        print("🧪 Running Aetherforge tests...")
        
        if args.component:
            print(f"🎯 Testing component: {args.component}")
            # Run specific component tests
            import subprocess
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                f'tests/test_{args.component}.py', '-v'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Component tests passed")
            else:
                print("❌ Component tests failed")
                print(result.stdout)
                print(result.stderr)
        
        elif args.integration:
            print("🔗 Running integration tests...")
            import subprocess
            result = subprocess.run([
                sys.executable, 'run_complete_tests.py'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Integration tests passed")
            else:
                print("❌ Integration tests failed")
                print(result.stdout)
        
        else:
            print("🧪 Running basic system tests...")
            # Run basic API key validation
            from api_manager import get_api_manager
            api_manager = get_api_manager()
            providers = api_manager.get_available_providers()
            
            if providers:
                print(f"✅ API manager working ({len(providers)} providers configured)")
            else:
                print("⚠️ No API providers configured")
            
            print("✅ Basic tests completed")
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")

if __name__ == "__main__":
    main()
