#!/usr/bin/env python3
"""
Comprehensive integration tests for API key management system
Tests all components: APIManager, SecureKeyStorage, CLI, and VS Code integration
"""

import pytest
import asyncio
import tempfile
import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import json

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from api_manager import APIManager, APIProvider, SecureKeyStorage, APIKeyValidator
from api_key_cli import APIKeyCLI

class TestSecureKeyStorage:
    """Test secure key storage functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = Path(self.temp_dir) / "test_keys.enc"
        self.storage = SecureKeyStorage(self.storage_path)
    
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_store_and_load_key(self):
        """Test basic key storage and retrieval"""
        test_key = "sk-test123456789"
        provider = "openai"
        
        # Store key
        self.storage.store_key(provider, test_key)
        
        # Verify file was created
        assert self.storage_path.exists()
        
        # Load key
        loaded_key = self.storage.load_key(provider)
        assert loaded_key == test_key
    
    def test_multiple_keys(self):
        """Test storing multiple provider keys"""
        keys = {
            "openai": "sk-openai123",
            "anthropic": "sk-ant-123",
            "azure": "azure-key-123"
        }
        
        # Store all keys
        for provider, key in keys.items():
            self.storage.store_key(provider, key)
        
        # Load all keys
        loaded_keys = self.storage.load_all_keys()
        assert loaded_keys == keys
    
    def test_key_encryption(self):
        """Test that keys are actually encrypted on disk"""
        test_key = "sk-test123456789"
        provider = "openai"
        
        self.storage.store_key(provider, test_key)
        
        # Read raw file content
        with open(self.storage_path, 'rb') as f:
            raw_content = f.read()
        
        # Verify the key is not stored in plain text
        assert test_key.encode() not in raw_content
    
    def test_invalid_provider(self):
        """Test handling of invalid provider names"""
        # Test empty provider
        with pytest.raises(ValueError):
            self.storage.store_key("", "test-key")
        
        # Test None provider
        with pytest.raises(ValueError):
            self.storage.store_key(None, "test-key")
    
    def test_invalid_key(self):
        """Test handling of invalid API keys"""
        # Test empty key
        with pytest.raises(ValueError):
            self.storage.store_key("openai", "")
        
        # Test None key
        with pytest.raises(ValueError):
            self.storage.store_key("openai", None)

class TestAPIKeyValidator:
    """Test API key validation functionality"""
    
    @pytest.mark.asyncio
    async def test_openai_validation_success(self):
        """Test successful OpenAI key validation"""
        with patch('openai.AsyncOpenAI') as mock_openai:
            # Mock successful response
            mock_client = AsyncMock()
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Hello"
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            result = await APIKeyValidator.validate_openai_key("sk-test123")
            
            assert result["valid"] is True
            assert result["provider"] == "openai"
            assert "message" in result
    
    @pytest.mark.asyncio
    async def test_openai_validation_failure(self):
        """Test failed OpenAI key validation"""
        with patch('openai.AsyncOpenAI') as mock_openai:
            # Mock authentication error
            mock_client = AsyncMock()
            mock_client.chat.completions.create.side_effect = Exception("Invalid API key")
            mock_openai.return_value = mock_client
            
            result = await APIKeyValidator.validate_openai_key("invalid-key")
            
            assert result["valid"] is False
            assert result["provider"] == "openai"
            assert "Invalid" in result["message"]
    
    @pytest.mark.asyncio
    async def test_anthropic_validation_success(self):
        """Test successful Anthropic key validation"""
        with patch('anthropic.AsyncAnthropic') as mock_anthropic:
            # Mock successful response
            mock_client = AsyncMock()
            mock_response = Mock()
            mock_response.content = [Mock()]
            mock_response.content[0].text = "Hello"
            mock_client.messages.create.return_value = mock_response
            mock_anthropic.return_value = mock_client
            
            result = await APIKeyValidator.validate_anthropic_key("sk-ant-test123")
            
            assert result["valid"] is True
            assert result["provider"] == "anthropic"
    
    @pytest.mark.asyncio
    async def test_azure_validation_success(self):
        """Test successful Azure key validation"""
        with patch('openai.AsyncAzureOpenAI') as mock_azure:
            # Mock successful response
            mock_client = AsyncMock()
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Hello"
            mock_client.chat.completions.create.return_value = mock_response
            mock_azure.return_value = mock_client
            
            result = await APIKeyValidator.validate_azure_key(
                "test-key", 
                "https://test.openai.azure.com"
            )
            
            assert result["valid"] is True
            assert result["provider"] == "azure"

class TestAPIManager:
    """Test APIManager functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = Path(self.temp_dir) / "test_keys.enc"
        
        # Create APIManager with test storage
        with patch('src.api_manager.SecureKeyStorage') as mock_storage_class:
            mock_storage = Mock()
            mock_storage_class.return_value = mock_storage
            self.api_manager = APIManager()
            self.api_manager.key_storage = mock_storage
    
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_set_api_key_success(self):
        """Test successful API key setting"""
        with patch.object(APIKeyValidator, 'validate_key') as mock_validate:
            mock_validate.return_value = {
                "valid": True,
                "provider": "openai",
                "message": "Valid key"
            }
            
            result = await self.api_manager.set_api_key(
                APIProvider.OPENAI, 
                "sk-test123", 
                validate=True
            )
            
            assert result["success"] is True
            assert "validation" in result
    
    @pytest.mark.asyncio
    async def test_set_api_key_validation_failure(self):
        """Test API key setting with validation failure"""
        with patch.object(APIKeyValidator, 'validate_key') as mock_validate:
            mock_validate.return_value = {
                "valid": False,
                "provider": "openai",
                "message": "Invalid key"
            }
            
            result = await self.api_manager.set_api_key(
                APIProvider.OPENAI, 
                "invalid-key", 
                validate=True
            )
            
            assert result["success"] is False
            assert "Invalid" in result["message"]
    
    def test_get_available_providers(self):
        """Test getting available providers"""
        # Mock storage to return some keys
        self.api_manager.key_storage.load_all_keys.return_value = {
            "openai": "sk-test123",
            "anthropic": "sk-ant-123"
        }
        
        providers = self.api_manager.get_available_providers()
        
        assert APIProvider.OPENAI in providers
        assert APIProvider.ANTHROPIC in providers
        assert APIProvider.AZURE not in providers
    
    def test_list_configured_providers(self):
        """Test listing configured providers"""
        # Mock storage and environment
        self.api_manager.key_storage.load_all_keys.return_value = {
            "openai": "sk-test123"
        }
        
        with patch.dict(os.environ, {"ANTHROPIC_API_KEY": "sk-ant-123"}):
            providers = self.api_manager.list_configured_providers()
            
            # Should find both stored and environment keys
            provider_names = [p["provider"] for p in providers]
            assert "openai" in provider_names
            assert "anthropic" in provider_names

class TestAPIKeyCLI:
    """Test CLI functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        with patch('src.api_key_cli.APIManager') as mock_manager_class:
            self.mock_manager = Mock()
            mock_manager_class.return_value = self.mock_manager
            self.cli = APIKeyCLI()
    
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_set_key_success(self):
        """Test successful key setting via CLI"""
        self.mock_manager.set_api_key.return_value = {
            "success": True,
            "message": "Key set successfully"
        }
        
        result = await self.cli.set_key("openai", "sk-test123", validate=False)
        
        assert result is True
        self.mock_manager.set_api_key.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_set_key_invalid_provider(self):
        """Test setting key with invalid provider"""
        result = await self.cli.set_key("invalid_provider", "sk-test123")
        
        assert result is False
        self.mock_manager.set_api_key.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_test_key_success(self):
        """Test successful key testing via CLI"""
        self.mock_manager.test_api_key.return_value = {
            "success": True,
            "validation": {"valid": True, "message": "Key is valid"}
        }
        
        result = await self.cli.test_key("openai")
        
        assert result is True
        self.mock_manager.test_api_key.assert_called_once()

class TestIntegration:
    """Integration tests for the complete system"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.storage_path = Path(self.temp_dir) / "integration_keys.enc"
    
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self):
        """Test complete workflow: store key, validate, use"""
        # Create real storage (but with test path)
        storage = SecureKeyStorage(self.storage_path)
        
        # Create API manager with test storage
        api_manager = APIManager()
        api_manager.key_storage = storage
        
        # Mock validation to avoid real API calls
        with patch.object(APIKeyValidator, 'validate_key') as mock_validate:
            mock_validate.return_value = {
                "valid": True,
                "provider": "openai",
                "message": "Valid key",
                "model_access": ["gpt-4", "gpt-3.5-turbo"]
            }
            
            # Set API key
            result = await api_manager.set_api_key(
                APIProvider.OPENAI, 
                "sk-test123456789", 
                validate=True
            )
            
            assert result["success"] is True
            
            # Verify key was stored
            stored_key = storage.load_key("openai")
            assert stored_key == "sk-test123456789"
            
            # Verify provider is available
            providers = api_manager.get_available_providers()
            assert APIProvider.OPENAI in providers
            
            # Test key validation
            test_result = await api_manager.test_api_key(APIProvider.OPENAI)
            assert test_result["success"] is True
    
    def test_cli_integration(self):
        """Test CLI integration with API manager"""
        # This would test the actual CLI commands
        # For now, we'll test the integration points
        
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = '{"success": true}'
            
            # Test that CLI can be called
            import subprocess
            result = subprocess.run([
                sys.executable, '-c', 
                'import sys; sys.path.insert(0, "src"); from api_key_cli import APIKeyCLI; print("CLI imported successfully")'
            ], capture_output=True, text=True)
            
            assert result.returncode == 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
