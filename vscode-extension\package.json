{"name": "aetherforge", "displayName": "Aetherforge", "description": "Autonomous AI Software Creation System", "version": "1.0.0", "publisher": "aetherforge", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "aetherforge.createProject", "title": "Create Project", "category": "Aetherforge", "icon": "$(add)"}, {"command": "aetherforge.quickCreate", "title": "Quick Create Project", "category": "Aetherforge", "icon": "$(rocket)"}, {"command": "aetherforge.createFromTemplate", "title": "Create from Template", "category": "Aetherforge", "icon": "$(file-code)"}, {"command": "aetherforge.showAgentPanel", "title": "Agent Interaction Panel", "category": "Aetherforge", "icon": "$(robot)"}, {"command": "aetherforge.chatWithAgent", "title": "Chat with Agent", "category": "Aetherforge", "icon": "$(comment)"}, {"command": "aetherforge.viewAgentStatus", "title": "View Agent Status", "category": "Aetherforge", "icon": "$(pulse)"}, {"command": "aetherforge.showWorkflow", "title": "Workflow Visualization", "category": "Aetherforge", "icon": "$(git-branch)"}, {"command": "aetherforge.viewProjectStatus", "title": "View Project Status", "category": "Aetherforge", "icon": "$(project)"}, {"command": "aetherforge.showPheromoneTrail", "title": "Pheromone Trail", "category": "Aetherforge", "icon": "$(graph)"}, {"command": "aetherforge.showSystemStatus", "title": "System Status", "category": "Aetherforge", "icon": "$(server)"}, {"command": "aetherforge.openSettings", "title": "Settings", "category": "Aetherforge", "icon": "$(gear)"}, {"command": "aetherforge.refreshConnection", "title": "Refresh Connection", "category": "Aetherforge", "icon": "$(refresh)"}, {"command": "aetherforge.workspace.createFile", "title": "Create File", "category": "Aetherforge Workspace", "icon": "$(new-file)"}, {"command": "aetherforge.workspace.createProject", "title": "Create Project", "category": "Aetherforge Workspace", "icon": "$(new-folder)"}, {"command": "aetherforge.workspace.createFromTemplate", "title": "Create from Template", "category": "Aetherforge Workspace", "icon": "$(file-code)"}, {"command": "aetherforge.workspace.searchFiles", "title": "Search in Files", "category": "Aetherforge Workspace", "icon": "$(search)"}, {"command": "aetherforge.workspace.backupFiles", "title": "Backup Files", "category": "Aetherforge Workspace", "icon": "$(archive)"}, {"command": "aetherforge.workspace.showInfo", "title": "Show Workspace Info", "category": "Aetherforge Workspace", "icon": "$(info)"}, {"command": "aetherforge.workspace.listFiles", "title": "List Project Files", "category": "Aetherforge Workspace", "icon": "$(list-tree)"}, {"command": "aetherforge.workspace.watchFiles", "title": "Watch Files", "category": "Aetherforge Workspace", "icon": "$(eye)"}, {"command": "aetherforge.workspace.showOperations", "title": "Show Operations", "category": "Aetherforge Workspace", "icon": "$(list-ordered)"}, {"command": "aetherforge.showAgentCommunication", "title": "Agent Communication", "category": "Aetherforge", "icon": "$(comment-discussion)"}, {"command": "aetherforge.configureApiKeys", "title": "Configure API Keys", "category": "Aetherforge", "icon": "$(key)"}], "menus": {"commandPalette": [{"command": "aetherforge.createProject", "when": "true"}, {"command": "aetherforge.quickCreate", "when": "true"}, {"command": "aetherforge.showAgentPanel", "when": "true"}, {"command": "aetherforge.showWorkflow", "when": "true"}, {"command": "aetherforge.showSystemStatus", "when": "true"}, {"command": "aetherforge.workspace.createFile", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.createProject", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.createFromTemplate", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.searchFiles", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.backupFiles", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.showInfo", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.listFiles", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.watchFiles", "when": "workspaceFolderCount > 0"}, {"command": "aetherforge.workspace.showOperations", "when": "true"}, {"command": "aetherforge.showAgentCommunication", "when": "true"}, {"command": "aetherforge.configureApiKeys", "when": "true"}], "explorer/context": [{"command": "aetherforge.createProject", "group": "aetherforge", "when": "explorerResourceIsFolder"}, {"command": "aetherforge.workspace.createFile", "group": "aetherforge@1", "when": "explorerResourceIsFolder"}, {"command": "aetherforge.workspace.createProject", "group": "aetherforge@2", "when": "explorerResourceIsFolder"}]}, "keybindings": [{"command": "aetherforge.createProject", "key": "ctrl+alt+a", "mac": "cmd+alt+a"}, {"command": "aetherforge.quickCreate", "key": "ctrl+alt+q", "mac": "cmd+alt+q"}, {"command": "aetherforge.showAgentPanel", "key": "ctrl+alt+g", "mac": "cmd+alt+g"}, {"command": "aetherforge.showWorkflow", "key": "ctrl+alt+w", "mac": "cmd+alt+w"}, {"command": "aetherforge.workspace.createFile", "key": "ctrl+alt+n", "mac": "cmd+alt+n"}, {"command": "aetherforge.workspace.createProject", "key": "ctrl+alt+p", "mac": "cmd+alt+p"}, {"command": "aetherforge.workspace.searchFiles", "key": "ctrl+alt+f", "mac": "cmd+alt+f"}, {"command": "aetherforge.showAgentCommunication", "key": "ctrl+alt+a", "mac": "cmd+alt+a"}], "configuration": {"title": "Aetherforge", "properties": {"aetherforge.orchestratorUrl": {"type": "string", "default": "http://localhost:8000", "description": "URL of the Aetherforge orchestrator service"}, "aetherforge.autoOpenProjects": {"type": "boolean", "default": true, "description": "Automatically open generated projects in VS Code"}, "aetherforge.showNotifications": {"type": "boolean", "default": true, "description": "Show notifications for project creation and agent activities"}, "aetherforge.defaultProjectType": {"type": "string", "default": "fullstack", "enum": ["fullstack", "frontend", "backend", "mobile", "desktop", "api", "game"], "description": "Default project type for quick creation"}, "aetherforge.defaultAgentBehavior": {"type": "string", "default": "balanced", "enum": ["conservative", "balanced", "aggressive", "creative", "production"], "description": "Default agent behavior for project creation"}, "aetherforge.enableRealTimeUpdates": {"type": "boolean", "default": true, "description": "Enable real-time updates via WebSocket connections"}, "aetherforge.projectsDirectory": {"type": "string", "default": "./projects", "description": "Default directory for generated projects"}, "aetherforge.logLevel": {"type": "string", "default": "info", "enum": ["debug", "info", "warn", "error"], "description": "Logging level for the extension"}, "aetherforge.enableAdvancedFeatures": {"type": "boolean", "default": false, "description": "Enable advanced features like pheromone trail visualization"}, "aetherforge.workspace.autoBackup": {"type": "boolean", "default": true, "description": "Automatically backup files before modification"}, "aetherforge.workspace.backupDirectory": {"type": "string", "default": ".aetherforge/backups", "description": "Directory for file backups"}, "aetherforge.workspace.watchPatterns": {"type": "array", "default": ["**/*.{js,ts,jsx,tsx,py,java,cpp,c,h}"], "description": "File patterns to watch for changes"}, "aetherforge.workspace.excludePatterns": {"type": "array", "default": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/.git/**"], "description": "File patterns to exclude from operations"}, "aetherforge.workspace.maxSearchResults": {"type": "number", "default": 1000, "description": "Maximum number of search results to return"}, "aetherforge.workspace.enableFileWatching": {"type": "boolean", "default": true, "description": "Enable real-time file watching"}, "aetherforge.apiKeys.openai": {"type": "string", "default": "", "description": "OpenAI API Key (stored securely)"}, "aetherforge.apiKeys.anthropic": {"type": "string", "default": "", "description": "Anthropic API Key (stored securely)"}, "aetherforge.apiKeys.azure.key": {"type": "string", "default": "", "description": "Azure OpenAI API Key (stored securely)"}, "aetherforge.apiKeys.azure.endpoint": {"type": "string", "default": "", "description": "Azure OpenAI Endpoint URL"}, "aetherforge.apiKeys.azure.deployment": {"type": "string", "default": "gpt-35-turbo", "description": "Azure OpenAI Deployment Name"}, "aetherforge.apiKeys.preferredProvider": {"type": "string", "default": "openai", "enum": ["openai", "anthropic", "azure", "local"], "description": "Preferred AI provider for project generation"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "install-extension": "code --install-extension *.vsix"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "esbuild": "^0.19.12", "typescript": "^4.9.4"}}